.map-setup-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 1rem;
}

.map-setup-header {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
}

.back-button:hover {
  background: #5a6268;
}

.map-setup-header h1 {
  margin: 0;
  color: #333;
  flex-grow: 1;
}

.map-setup-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.map-choice-container {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.choice-cards {
  display: flex;
  gap: 2rem;
  max-width: 800px;
}

.choice-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  flex: 1;
  min-width: 250px;
}

.choice-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.choice-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.choice-card h3 {
  color: #333;
  margin-bottom: 1rem;
}

.choice-card p {
  color: #666;
  margin: 0;
}

.existing-maps-container {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.maps-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.map-item {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
}

.map-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.map-item.selected {
  border-color: #007bff;
  background: #e3f2fd;
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
}

.map-item.config {
  border-left: 4px solid #28a745;
}

.map-item.user {
  border-left: 4px solid #ffc107;
}

.map-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.map-item-header h4 {
  margin: 0;
  color: #333;
  flex-grow: 1;
}

.source-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.source-badge.config {
  background: #d4edda;
  color: #155724;
}

.source-badge.user {
  background: #fff3cd;
  color: #856404;
}

.map-item-details {
  margin-bottom: 1rem;
}

.map-item-details p {
  margin: 0.25rem 0;
  color: #666;
  font-size: 0.9rem;
}

.map-item small {
  color: #999;
  font-size: 0.8rem;
  display: block;
  margin-top: 0.5rem;
}

.no-maps-message {
  text-align: center;
  padding: 3rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.no-maps-message p {
  color: #6c757d;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.create-new-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.create-new-btn:hover {
  background: #0056b3;
}

.map-preview {
  margin-top: 2rem;
}

.map-preview h4 {
  margin-bottom: 1rem;
  color: #333;
}

.new-map-container {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.new-map-controls {
  margin-bottom: 2rem;
}

.map-name-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.map-name-input:focus {
  outline: none;
  border-color: #007bff;
}

.drawing-controls {
  margin-bottom: 2rem;
}

.drawing-status {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.status-message {
  margin: 0;
  color: #495057;
  font-size: 0.95rem;
}

.status-message.active {
  color: #007bff;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.control-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.draw-btn, .finish-btn, .clear-btn, .save-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.draw-btn {
  background: #007bff;
  color: white;
}

.draw-btn:hover {
  background: #0056b3;
}

.draw-btn.active {
  background: #28a745;
}

.draw-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.finish-btn {
  background: #ffc107;
  color: #212529;
}

.finish-btn:hover {
  background: #e0a800;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
}

.map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-setup-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.back-choice-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
}

.back-choice-btn:hover {
  background: #5a6268;
}

.continue-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
}

.continue-btn:hover {
  background: #218838;
}

.continue-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .choice-cards {
    flex-direction: column;
    align-items: center;
  }
  
  .drawing-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .map-setup-footer {
    flex-direction: column;
    gap: 1rem;
  }
}
