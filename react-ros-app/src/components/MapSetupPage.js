import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './MapSetupPage.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

const MapSetupPage = ({ onMapSetupComplete, onBack }) => {
  const [mapChoice, setMapChoice] = useState(null); // 'existing' or 'new'
  const [existingMaps, setExistingMaps] = useState([]);
  const [selectedMap, setSelectedMap] = useState(null);
  const [newMapData, setNewMapData] = useState({
    boundary: [],
    startPoint: null,
    endPoint: null,
    name: ''
  });
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingMode, setDrawingMode] = useState(null); // 'boundary', 'start', 'end'

  useEffect(() => {
    // Load existing maps from localStorage or API
    loadExistingMaps();
  }, []);

  const loadExistingMaps = async () => {
    try {
      // Try to load from the config file first
      const response = await fetch('/weednix_sensors/config/boundaries.geojson');
      if (response.ok) {
        const geoJsonData = await response.json();
        const maps = [{
          id: 'default',
          name: 'Default Field Boundary',
          data: geoJsonData,
          boundary: geoJsonData.features[0]?.geometry?.coordinates[0] || []
        }];
        setExistingMaps(maps);
      }
    } catch (error) {
      console.log('No existing maps found, starting fresh');
    }

    // Also check localStorage for user-created maps
    const savedMaps = JSON.parse(localStorage.getItem('savedMaps') || '[]');
    setExistingMaps(prev => [...prev, ...savedMaps]);
  };

  const MapClickHandler = () => {
    useMapEvents({
      click: (e) => {
        if (!isDrawing) return;

        const { lat, lng } = e.latlng;
        
        if (drawingMode === 'boundary') {
          setNewMapData(prev => ({
            ...prev,
            boundary: [...prev.boundary, [lng, lat]]
          }));
        } else if (drawingMode === 'start') {
          setNewMapData(prev => ({
            ...prev,
            startPoint: [lng, lat]
          }));
          setDrawingMode(null);
          setIsDrawing(false);
        } else if (drawingMode === 'end') {
          setNewMapData(prev => ({
            ...prev,
            endPoint: [lng, lat]
          }));
          setDrawingMode(null);
          setIsDrawing(false);
        }
      }
    });
    return null;
  };

  const startDrawing = (mode) => {
    setDrawingMode(mode);
    setIsDrawing(true);
  };

  const finishBoundary = () => {
    if (newMapData.boundary.length >= 3) {
      // Close the polygon by adding the first point at the end
      setNewMapData(prev => ({
        ...prev,
        boundary: [...prev.boundary, prev.boundary[0]]
      }));
      setDrawingMode(null);
      setIsDrawing(false);
    }
  };

  const clearDrawing = () => {
    setNewMapData({
      boundary: [],
      startPoint: null,
      endPoint: null,
      name: ''
    });
    setIsDrawing(false);
    setDrawingMode(null);
  };

  const saveNewMap = () => {
    if (!newMapData.name || newMapData.boundary.length < 4) {
      alert('Please provide a map name and complete the boundary drawing');
      return;
    }

    const mapToSave = {
      id: Date.now().toString(),
      name: newMapData.name,
      boundary: newMapData.boundary,
      startPoint: newMapData.startPoint,
      endPoint: newMapData.endPoint,
      createdAt: new Date().toISOString()
    };

    const savedMaps = JSON.parse(localStorage.getItem('savedMaps') || '[]');
    savedMaps.push(mapToSave);
    localStorage.setItem('savedMaps', JSON.stringify(savedMaps));

    setExistingMaps(prev => [...prev, mapToSave]);
    setSelectedMap(mapToSave);
    setMapChoice('existing');
  };

  const handleContinue = () => {
    if (mapChoice === 'existing' && selectedMap) {
      onMapSetupComplete({
        type: 'existing',
        mapData: selectedMap
      });
    } else if (mapChoice === 'new' && newMapData.boundary.length >= 4) {
      onMapSetupComplete({
        type: 'new',
        mapData: newMapData
      });
    }
  };

  const canContinue = () => {
    return (mapChoice === 'existing' && selectedMap) || 
           (mapChoice === 'new' && newMapData.boundary.length >= 4);
  };

  return (
    <div className="map-setup-page">
      <header className="map-setup-header">
        <button className="back-button" onClick={onBack}>← Back</button>
        <h1>Mission Map Setup</h1>
        <p>Choose an existing map or create a new field boundary</p>
      </header>

      {!mapChoice && (
        <div className="map-choice-container">
          <div className="choice-cards">
            <div className="choice-card" onClick={() => setMapChoice('existing')}>
              <div className="choice-icon">📋</div>
              <h3>Use Existing Map</h3>
              <p>Select from previously saved field boundaries</p>
            </div>
            <div className="choice-card" onClick={() => setMapChoice('new')}>
              <div className="choice-icon">✏️</div>
              <h3>Create New Map</h3>
              <p>Draw a new field boundary on the map</p>
            </div>
          </div>
        </div>
      )}

      {mapChoice === 'existing' && (
        <div className="existing-maps-container">
          <h3>Select Existing Map</h3>
          <div className="maps-list">
            {existingMaps.map(map => (
              <div 
                key={map.id} 
                className={`map-item ${selectedMap?.id === map.id ? 'selected' : ''}`}
                onClick={() => setSelectedMap(map)}
              >
                <h4>{map.name}</h4>
                <p>Boundary points: {map.boundary?.length || 0}</p>
                {map.createdAt && <small>Created: {new Date(map.createdAt).toLocaleDateString()}</small>}
              </div>
            ))}
          </div>
          
          {selectedMap && (
            <div className="map-preview">
              <h4>Map Preview: {selectedMap.name}</h4>
              <MapContainer
                center={[29.9778, 30.9473]} // Default center, will be adjusted based on boundary
                zoom={18}
                style={{ height: '300px', width: '100%' }}
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                />
                {selectedMap.boundary && selectedMap.boundary.length > 0 && (
                  <Polygon
                    positions={selectedMap.boundary.map(coord => [coord[1], coord[0]])}
                    color="blue"
                    fillColor="lightblue"
                    fillOpacity={0.3}
                  />
                )}
                {selectedMap.startPoint && (
                  <Marker position={[selectedMap.startPoint[1], selectedMap.startPoint[0]]} />
                )}
                {selectedMap.endPoint && (
                  <Marker position={[selectedMap.endPoint[1], selectedMap.endPoint[0]]} />
                )}
              </MapContainer>
            </div>
          )}
        </div>
      )}

      {mapChoice === 'new' && (
        <div className="new-map-container">
          <div className="new-map-controls">
            <input
              type="text"
              placeholder="Enter map name"
              value={newMapData.name}
              onChange={(e) => setNewMapData(prev => ({ ...prev, name: e.target.value }))}
              className="map-name-input"
            />
            
            <div className="drawing-controls">
              <button 
                className={`draw-btn ${drawingMode === 'boundary' ? 'active' : ''}`}
                onClick={() => startDrawing('boundary')}
                disabled={isDrawing && drawingMode !== 'boundary'}
              >
                {drawingMode === 'boundary' ? 'Drawing Boundary...' : 'Draw Boundary'}
              </button>
              
              {newMapData.boundary.length >= 3 && (
                <button className="finish-btn" onClick={finishBoundary}>
                  Finish Boundary
                </button>
              )}
              
              <button 
                className={`draw-btn ${drawingMode === 'start' ? 'active' : ''}`}
                onClick={() => startDrawing('start')}
                disabled={isDrawing && drawingMode !== 'start'}
              >
                Set Start Point
              </button>
              
              <button 
                className={`draw-btn ${drawingMode === 'end' ? 'active' : ''}`}
                onClick={() => startDrawing('end')}
                disabled={isDrawing && drawingMode !== 'end'}
              >
                Set End Point
              </button>
              
              <button className="clear-btn" onClick={clearDrawing}>
                Clear All
              </button>
              
              {newMapData.boundary.length >= 4 && (
                <button className="save-btn" onClick={saveNewMap}>
                  Save Map
                </button>
              )}
            </div>
          </div>

          <div className="map-container">
            <MapContainer
              center={[29.9778, 30.9473]}
              zoom={18}
              style={{ height: '500px', width: '100%' }}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
              <MapClickHandler />
              
              {newMapData.boundary.length > 2 && (
                <Polygon
                  positions={newMapData.boundary.map(coord => [coord[1], coord[0]])}
                  color="red"
                  fillColor="lightcoral"
                  fillOpacity={0.3}
                />
              )}
              
              {newMapData.startPoint && (
                <Marker 
                  position={[newMapData.startPoint[1], newMapData.startPoint[0]]}
                />
              )}
              
              {newMapData.endPoint && (
                <Marker 
                  position={[newMapData.endPoint[1], newMapData.endPoint[0]]}
                />
              )}
            </MapContainer>
          </div>
        </div>
      )}

      {mapChoice && (
        <div className="map-setup-footer">
          <button className="back-choice-btn" onClick={() => setMapChoice(null)}>
            ← Change Choice
          </button>
          <button 
            className="continue-btn" 
            onClick={handleContinue}
            disabled={!canContinue()}
          >
            Continue to Mission Parameters →
          </button>
        </div>
      )}
    </div>
  );
};

export default MapSetupPage;
